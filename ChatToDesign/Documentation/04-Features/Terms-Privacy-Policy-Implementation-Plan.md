# Terms of Service & Privacy Policy 实现技术方案

## 概述

为 Picadabra AI 应用添加 Terms of Service (服务条款) 和 Privacy Policy (隐私政策) 功能，在设置页面提供入口，用户可以查看相关法律文档。

## 当前架构分析

### 现有设置页面结构
- **位置**: `ChatToDesign/Presentation/Profile/ProfileSettingsView.swift`
- **架构**: MVVM 模式，使用 `ProfileSettingsViewModel`
- **UI 结构**: 分组设置项 (Account, Subscription, Support)
- **导航**: 使用 NavigationView 和 fullScreenCover 模式

### 项目架构特点
- **Clean Architecture**: Domain/Infrastructure/Presentation 分层
- **依赖注入**: 使用 `AppDependencyContainer`
- **设计系统**: 自定义 DesignSystem 组件
- **导航模式**: 偏好使用 fullScreenCover 而非 sheet

## 技术方案设计

### 1. 文档内容准备

#### 1.1 创建 Picadabra AI 专用文档
基于现有的 `pollo-ai-tos.md` 模板，创建：
- `picadabra-ai-tos.md` - 服务条款
- `picadabra-ai-privacy-policy.md` - 隐私政策

#### 1.2 文档内容适配
- 品牌名称: Pollo.ai → Picadabra AI
- 域名和联系方式更新
- 功能描述适配 (AI 图像/视频生成)

### 2. UI 组件实现

#### 2.1 WebView 组件
创建通用的文档查看组件：
```
ChatToDesign/Presentation/Common/Components/
├── DocumentWebView.swift          # WebView 封装组件
└── DocumentViewerSheet.swift      # 文档查看器容器
```

**技术选择**: 使用 `WKWebView` + `SafariServices` 的混合方案
- 本地 Markdown 渲染使用 WKWebView
- 外部链接使用 SFSafariViewController

#### 2.2 设置页面集成
在 `ProfileSettingsView.swift` 的 "Support" 分组中添加：
- Terms of Service 入口
- Privacy Policy 入口

### 3. 数据层实现

#### 3.1 文档服务
```
ChatToDesign/Domain/Services/
└── DocumentService.swift          # 文档内容管理服务
```

#### 3.2 文档实体
```
ChatToDesign/Domain/Entities/
└── LegalDocument.swift           # 法律文档实体定义
```

### 4. 架构设计

#### 4.1 组件层次结构
```
ProfileSettingsView
├── Support Section
│   ├── Terms of Service Row
│   └── Privacy Policy Row
└── DocumentViewerSheet (fullScreenCover)
    └── DocumentWebView
        └── WKWebView / SFSafariViewController
```

#### 4.2 数据流
```
User Tap → ProfileSettingsView → DocumentService → DocumentWebView
                ↓
        Load Markdown Content → Render HTML → Display
```

### 5. 实现细节

#### 5.1 文档加载策略
- **本地优先**: 从 Bundle 加载 Markdown 文件
- **在线备份**: 支持从远程 URL 加载最新版本
- **缓存机制**: 使用现有的缓存系统

#### 5.2 Markdown 渲染
- 使用 JavaScript 库 (如 marked.js) 在 WKWebView 中渲染
- 自定义 CSS 样式匹配应用设计系统
- 支持深色模式

#### 5.3 用户体验优化
- **加载状态**: 显示加载指示器
- **错误处理**: 网络错误时显示友好提示
- **导航**: 支持返回按钮和关闭手势
- **可访问性**: 支持 VoiceOver 和动态字体

### 6. 文件结构

```
ChatToDesign/
├── Documentation/
│   ├── picadabra-ai-tos.md                    # 服务条款
│   └── picadabra-ai-privacy-policy.md         # 隐私政策
├── Domain/
│   ├── Entities/
│   │   └── LegalDocument.swift
│   └── Services/
│       └── DocumentService.swift
├── Infrastructure/
│   └── Services/
│       └── DocumentServiceImpl.swift
└── Presentation/
    ├── Common/
    │   └── Components/
    │       ├── DocumentWebView.swift
    │       └── DocumentViewerSheet.swift
    └── Profile/
        └── ProfileSettingsView.swift (修改)
```

### 7. 开发步骤

1. **文档准备** (优先级: 高)
   - 创建 Picadabra AI 版本的 TOS 和 Privacy Policy
   - 准备 HTML 模板和 CSS 样式

2. **核心组件开发** (优先级: 高)
   - 实现 DocumentWebView 组件
   - 实现 DocumentViewerSheet 容器

3. **服务层实现** (优先级: 中)
   - 创建 DocumentService 接口和实现
   - 集成到依赖注入容器

4. **UI 集成** (优先级: 高)
   - 修改 ProfileSettingsView 添加入口
   - 测试导航和用户体验

5. **优化和测试** (优先级: 中)
   - 性能优化和错误处理
   - 可访问性测试
   - 多设备适配测试

### 8. 技术考虑

#### 8.1 性能优化
- Markdown 预编译为 HTML
- 图片和样式资源优化
- WebView 内存管理

#### 8.2 安全考虑
- 内容安全策略 (CSP)
- 防止 XSS 攻击
- 外部链接安全检查

#### 8.3 维护性
- 文档版本管理
- 多语言支持预留
- A/B 测试支持

## 总结

该方案遵循项目现有的 Clean Architecture 模式，使用 MVVM 架构，集成到现有的设置页面中。通过 WebView 组件提供良好的文档阅读体验，同时保持与应用整体设计的一致性。

实现后用户可以在设置页面方便地访问服务条款和隐私政策，满足应用商店审核要求和法律合规需求。
